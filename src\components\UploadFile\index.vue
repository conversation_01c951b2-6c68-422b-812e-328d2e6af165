<template>
    <div class="upload-file-container">
        <a-upload-dragger
            v-bind="$attrs"
            :file-list="fileList"
            :before-upload="beforeUpload"
            @remove="handleRemove"
            :custom-request="customRequest"
            :multiple="multiple"
            :accept="accept"
            :disabled="disabled"
            :max-count="maxCount"
            :show-upload-list="showUploadList">
            <slot>
                <div class="dragger-content">
                    <img class="upload-icon" src="@/assets/images/bid-examine/upload-circle.png" alt="上传">
                    <p class="dragger-text">{{ buttonText }}</p>
                    <p class="dragger-sub">仅支持 .docx 格式文档，单个文档大小不超过 20MB</p>
                    <p class="dragger-hint">或将文件拖拽到此处</p>
                </div>
            </slot>
            <template #itemRender="{ file, actions }">
                <div class="upload-list-item">
                    <div class="upload-list-item-info">
                        <div class="upload-list-item-name">
                            <file-outlined /> {{ file.name }}
                        </div>
                        <div v-if="file.status === 'uploading'" class="upload-list-item-progress">
                            <a-progress :percent="file.percent || 0" size="small" />
                        </div>
                    </div>
                    <div class="upload-list-item-actions">
                        <a-button type="link" @click="() => actions.remove()">
                            <delete-outlined />
                        </a-button>
                    </div>
                </div>
            </template>
        </a-upload-dragger>

        <div v-if="tip" class="upload-tip">{{ tip }}</div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { message } from 'ant-design-vue';
import { FileOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import type { UploadFile } from 'ant-design-vue';
import { formatFileSize } from '@/utils/tools'
import { fileUpload } from '@/api/examine';
interface Props {
  files?: any[]
  customerFn?: any
  buttonText?: string
  tip?: string
  multiple?: boolean
  accept?: string
  acceptTip?: string
  disabled?: boolean
  maxCount?: number
  maxSize?: number
  showUploadList?: boolean | object
  data?: object
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  files: () => [],
  customerFn: () => fileUpload,
  buttonText: '上传文件',
  tip: '',
  multiple: false,
  accept: '',
  acceptTip: '',
  disabled: false,
  maxCount: 10,
  maxSize: 20,
  showUploadList: true,
  data: () => ({})
})

// 定义事件
const emit = defineEmits(['update:files', 'change', 'success', 'error'])
const fileList = computed({
    get: () => props.files,
    set: (val) => {
        emit('update:files', val)
        emit('change', val)
    }
});

// 上传前校验
const beforeUpload = (file: File) => {
    const { accept , acceptTip } = props
    // 文件名安全检查
    const unsafeChars = /[\\/:*?"<>|\s]/g
    if (unsafeChars.test(file.name)) {
        message.info('文件名包含非法字符（\\ / : * ? " < > | 或空格），请修改后重试')
        return false
    }
    // 检查文件名长度
    if (file.name.length > 200) {
        message.info('文件名过长，请修改后重试')
        return false
    }
    // 空文件检查
    if (file.size === 0) {
        message.info('不能上传空文件');
        return false;
    }
    // 文件大小限制
    const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize;
    if (!isLtMaxSize) {
        message.info(`单个文档大小不超过 ${props.maxSize}MB!`)
        return false;
    }

    // 文件数量限制
    if (fileList.value.length >= props.maxCount) {
        message.info(`最多只能上传 ${props.maxCount} 份文件!`)
        return false
    }
    // 文件类型限制
    if (accept) {
        const acceptTypes = accept.split(',').map(type => type.trim().toLowerCase())
        const fileType = file.type.toLowerCase()
        const fileName = file.name.toLowerCase()
        const isValidType = acceptTypes.some(type => {
            if (type.startsWith('.')) {
                // 检查文件扩展名
                return fileName.endsWith(type)
            } else {
                // 检查 MIME 类型
                return fileType === type || fileType.startsWith(`${type}/`)
            }
        });
        if (!isValidType) {
            message.info (acceptTip || `只能上传 ${accept} 格式的文件！`)
            return false;
        }
    }
    return true
};

// 更新文件列表
const updateFileList = (newList: UploadFile[]) => {
    fileList.value = newList
};

// 添加更详细的类型定义
interface UploadConfig {
  signal: AbortSignal;
  onUploadProgress: (progressEvent: ProgressEvent) => void;
}

interface CustomRequestOptions {
  file: File;
  onSuccess: (response: any) => void;
  onError: (error: any) => void;
  onProgress: (event: { percent: number }) => void;
}

// 优化文件状态更新函数
const updateFileStatus = (uid: string, updates: Partial<UploadFile>) => {
  const index = fileList.value.findIndex(item => item.uid === uid)
  if (index !== -1) {
    const newList = [...fileList.value]
    // if(updates.status === 'error') {
    //    newList.splice(index, 1)
    // }else {
    newList[index] = {
      ...newList[index],
      ...updates
    }
    updateFileList(newList)
  }
}

// 优化自定义上传函数
const customRequest = async (options: CustomRequestOptions) => {
  const { file, onSuccess, onError, onProgress } = options
  // 创建上传文件对象
  const uploadFile: UploadFile = {
    uid: String(Date.now()),
    name: file.name,
    type: file.name?.split('.').pop()?.toUpperCase() || '',
    size: formatFileSize(file.size),
    status: 'uploading',
    percent: 0,
    originFileObj: file // 保存原始文件对象，用于重试上传
  }

  try {
    // 立即将文件添加到列表
    updateFileList([...fileList.value, uploadFile])
    const formData = new FormData()
    formData.append('file', file)

    // 添加额外参数
    if (props.data) {
      Object.entries(props.data).forEach(([key, value]) => {
        formData.append(key, value as string)
      })
    }

    // 创建上传配置
    const controller = new AbortController()
    const config: UploadConfig = {
      signal: controller.signal,
      onUploadProgress: (progressEvent: ProgressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1))
        updateFileStatus(uploadFile.uid, { percent, status: 'uploading' })
        onProgress({ percent })
      }
    }
    // 执行上传
    const uploadFn = typeof props.customerFn === 'function' ? props.customerFn : fileUpload
    const { err, data = {} } = await uploadFn(formData, config)
    if (err) {
      throw err
    }
    // 更新成功状态
    updateFileStatus(uploadFile.uid, {
      status: 'done',
      response: data,
      percent: 100
    })
    onSuccess(data)
    emit('success', uploadFile)
    return {
      abort: () => controller.abort()
    }

  } catch (error) {
    // 统一错误处理
    updateFileStatus(uploadFile.uid, { status: 'error' })
    emit('error', uploadFile)
    onError(error)
    console.error('Upload failed:', error)
  }
}

// 优化文件移除函数
const handleRemove = async (file: UploadFile) => {
  try {
    const index = fileList.value.findIndex(item => item.uid === file.uid)
    if (index === -1) return false

    const newList = [...fileList.value]
    newList.splice(index, 1)
    updateFileList(newList)
    emit('change', newList)
    return true
  } catch (error) {
    console.error('Remove file failed:', error)
    return false
  }
}

// 重试上传函数
const handleRetry = async (file: UploadFile) => {
  try {
    // 检查是否有原始文件对象
    if (!file.originFileObj) {
      console.error('无法重试上传：缺少原始文件对象')
      return false
    }

    // 更新文件状态为上传中
    updateFileStatus(file.uid, {
      status: 'uploading',
      percent: 0
    })

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file.originFileObj)

    // 添加额外参数
    if (props.data) {
      Object.entries(props.data).forEach(([key, value]) => {
        formData.append(key, value as string)
      })
    }

    // 创建上传配置
    const controller = new AbortController()
    const config: UploadConfig = {
      signal: controller.signal,
      onUploadProgress: (progressEvent: ProgressEvent) => {
        const percent = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1))
        updateFileStatus(file.uid, { percent, status: 'uploading' })
      }
    }

    // 执行上传
    const uploadFn = typeof props.customerFn === 'function' ? props.customerFn : fileUpload
    const { err, data = {} } = await uploadFn(formData, config)

    if (err) {
      throw err
    }

    // 更新成功状态
    updateFileStatus(file.uid, {
      status: 'done',
      response: data,
      percent: 100
    })

    emit('success', file)
    return true

  } catch (error) {
    // 重试失败，恢复错误状态
    updateFileStatus(file.uid, { status: 'error' })
    emit('error', file)
    console.error('Retry upload failed:', error)
    return false
  }
}

defineExpose({
    handleRemove,
    handleRetry
})
</script>

<style scoped lang="scss">
.upload-file-container {
  width: 100%;
}

.dragger-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  .upload-icon {
    width: 64px;
    height: 64px;
    display: block;
  }

  .dragger-text {
    font-size: 20px;
    font-weight: 400;
    color: #111827;
    margin: 16px 0 12px 0;
    text-align: center;
  }

  .dragger-sub {
    font-size: 16px;
    color: #4B5563;
    margin: 0 0 12px 0;
    text-align: center;
  }

  .dragger-hint {
    font-size: 14px;
    color: #6B7280;
    margin: 0;
    text-align: center;
  }
}

.upload-tip {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
}

.upload-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.upload-list-item-info {
    flex: 1;
    overflow: hidden;
}

.upload-list-item-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.upload-list-item-progress {
    margin-top: 4px;
}

.upload-list-item-actions {
    margin-left: 8px;
}
</style>