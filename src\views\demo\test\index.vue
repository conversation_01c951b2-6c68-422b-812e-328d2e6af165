<template>
    <div class="document-editor-container">
        <DocumentEditor
            v-if="isComponentReady"
            id="docEditor"
            document-server-url="http://************:8090/"
            :config="config"
            :events-on-document-ready="onDocumentReady"
            :on-load-component-error="onLoadComponentError"
        />
        <div v-else class="loading">
            正在加载文档编辑器...
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';

// 响应式数据
const isComponentReady = ref(false);

const config = reactive({
        document: {
            fileType: 'docx',
            key: 'wps-style-' + Date.now(),
            title: '测试文件.docx',
            url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
            // permissions: {
            //     edit: true,
            //     download: false,
            //     print: true,
            //     review: true
            // },
            // info: {
            //     language: 'zh-CN',
            //     documentGrid: {
            //         type: 'lines',
            //         linePitch: 432  /* 24磅行高（12pt×1.2×20） */
            //     },
            //     header: {
            //         height: 15,
            //         margin: 0
            //     },
            //     footer: {
            //         height: 15,
            //         margin: 0
            //     },
            //     pageMargins: {
            //         top: 37,
            //         bottom: 35,
            //         left: 28,
            //         right: 26
            //     }
            // },
            // pageSize: {
            //     width: 21,
            //     height: 29.7,
            //     unit: 'cm'
            // }
        },
        documentType: 'word',
        editorConfig: {
            mode: 'edit',
            lang: 'zh-CN',
            customization: {
                // autosave: true,
                // compactHeader: true,
                // toolbarHide: false,
                // zoom: 105,
                features: {
                    spellcheck: false,
                    ruler: true,  /* 显示标尺辅助调整 */
                    pageNavigation: false,
                    leftMenu: false,
                    rightMenu: false,
                    header: true,  /* 启用页眉 */
                    footer: true   /* 启用页脚 */
                },
                // uiTheme: {
                //     primaryColor: '#3498DB',
                //     toolbarBackground: '#000000'
                // }
            },
            user: {
                id: 'wps_user_001',
                name: 'WPS用户'
            }
        },
        // events: {
        //     onAppReady: () => {
        //         setTimeout(() => {
        //             // 强制应用样式
        //             document.querySelectorAll('.o_paragraph').forEach(p => {
        //                 p.style.lineHeight = '14.4pt';
        //                 p.style.marginBottom = '4.23mm';
        //             });

        //             // 调整页眉页脚内容位置
        //             const header = document.querySelector('.o_header');
        //             if(header) header.style.paddingTop = '2mm';

        //             const footer = document.querySelector('.o_footer');
        //             if(footer) footer.style.paddingBottom = '2mm';
        //         }, 2000);
        //     },
        //     onRequestSave: (event) => {
        //         console.log('文档变更数据:', event.data);
        //         // 此处添加保存逻辑
        //     }
        // }
    });

// 方法定义
const onDocumentReady = () => {
    console.log('Document is loaded');
};

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.log('Error code:', errorCode,errorDescription);
};

// 生命周期钩子
onMounted(async () => {
    nextTick(() => {
        isComponentReady.value = true;
    });
});
</script>

<style scoped>
.document-editor-container {
    width: 100%;
    height: 100vh;
    position: relative;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}
</style>