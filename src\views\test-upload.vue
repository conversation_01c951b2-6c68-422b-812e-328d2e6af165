<template>
  <div class="test-upload-page">
    <h2>拖拽上传测试页面</h2>
    
    <div class="upload-section">
      <upload-file
        ref="uploadFileRef"
        v-model:files="fileList"
        accept=".docx,.pdf,.txt"
        accept-tip="文件格式不正确，请选择.docx、.pdf或.txt文件"
        :show-upload-list="false"
        @change="doChange"
        @success="onSuccess"
        @error="onError">
        <div class="upload-area">
          <div class="upload-icon">📁</div>
          <div class="upload-text-main">上传文件测试</div>
          <div class="upload-text-sub">支持 .docx、.pdf、.txt 格式文档</div> 
          <div class="upload-text-hint">或将文件拖拽到此处</div>
        </div>
      </upload-file>
    </div>

    <div class="file-list-section" v-if="fileList.length > 0">
      <h3>文件列表 ({{ fileList.length }})</h3>
      <div class="file-items">
        <div v-for="file in fileList" :key="file.uid" class="file-item">
          <div class="file-info">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-status">状态: {{ file.status }}</div>
            <div class="file-progress" v-if="file.status === 'uploading'">
              进度: {{ file.percent || 0 }}%
            </div>
          </div>
          <button @click="removeFile(file)" class="remove-btn">删除</button>
        </div>
      </div>
    </div>

    <div class="debug-section">
      <h3>调试信息</h3>
      <div class="debug-info">
        <div>上传成功次数: {{ successCount }}</div>
        <div>上传失败次数: {{ errorCount }}</div>
        <div>文件变更次数: {{ changeCount }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UploadFile from '@/components/UploadFile/index.vue'

interface FileItem {
  uid: string
  name: string
  size: string
  status: 'uploading' | 'done' | 'error'
  percent?: number
  response?: any
}

const fileList = ref<FileItem[]>([])
const uploadFileRef = ref()

// 调试计数器
const successCount = ref(0)
const errorCount = ref(0)
const changeCount = ref(0)

function doChange(files: any) { 
  fileList.value = [...files]
  changeCount.value++
  console.log('文件列表变更:', files)
}

function onSuccess(file: any) {
  successCount.value++
  console.log('上传成功:', file)
}

function onError(file: any) {
  errorCount.value++
  console.log('上传失败:', file)
}

function removeFile(file: FileItem) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
.test-upload-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.upload-section {
  margin: 20px 0;
}

.upload-area {
  width: 100%;
  height: 200px;
  background: #F7F8FA;
  border: 1px solid #E5E6EB;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;     
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-5px);
  } 

  .upload-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .upload-text-main { 
    font-weight: 500;
    font-size: 18px;
    color: #111827;
    margin-bottom: 8px;
  }

  .upload-text-sub {  
    font-size: 14px; 
    color: #4B5563;
    margin-bottom: 8px;
  }

  .upload-text-hint { 
    color: #6B7280;
    font-size: 12px;
  }
}

.file-list-section {
  margin: 20px 0;
}

.file-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #E5E6EB;
  border-radius: 8px;
  background: #fff;
}

.file-info {
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.file-status, .file-progress {
  font-size: 12px;
  color: #666;
}

.remove-btn {
  padding: 4px 8px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-section {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.debug-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
}
</style>
