{"compilerOptions": {"baseUrl": "./", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "vue"]}, "include": ["./src/**/*.ts", "./src/**/*.d.ts", "./src/**/*.tsx", "./src/**/*.vue"], "exclude": ["node_modules", "dist", "service"]}