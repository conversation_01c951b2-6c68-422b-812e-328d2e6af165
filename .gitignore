# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

#node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables files
/robotjsDist/
/backup/
/service/.env
package-lock.json
yarn.lock
components.d.ts
node_modules
pdfjs-4.4.168-dist
pdfjs-v5.4.54-dist
doc
.promptx