import { reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { customDocumentBundleExport } from '@/api/download'

export interface ExportOption {
  key: string
  label: string
  fileType: string
  defaultChecked?: boolean
}

export function useExport(options: ExportOption[], taskId: string, fn?: Function) {
  const state = reactive({
    visible: false,
    loading: false,
    options: options.reduce((acc, option) => {
      acc[option.key] = option.defaultChecked ?? true
      return acc
    }, {} as Record<string, boolean>)
  })

  const hasSelectedOptions = computed(() => {
    return Object.values(state.options).some(<PERSON><PERSON><PERSON>)
  })

  const selectedFileTypes = computed(() => {
    return options
      .filter(option => state.options[option.key])
      .map(option => option.fileType)
  })
  const resetOptions = () => {
    options.forEach(option => {
      state.options[option.key] = option.defaultChecked ?? true
    })
  }
  const cancel = () => {
    state.visible = false
  }

  const confirm = async () => {
    if (selectedFileTypes.value.length === 0) {
      message.info('请至少选择一个导出选项')
      return
    }
    state.loading = true 
    await customDocumentBundleExport({
      fileTypes: selectedFileTypes.value,
      taskId
    })
    state.loading = false 
    state.visible = false 
  }
  const show = () => {
    resetOptions()
    state.visible = true
  }
  return {
    state,
    show,
    hasSelectedOptions,
    cancel,
    confirm
  }
}