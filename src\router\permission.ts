import type { Router } from 'vue-router'
import type {  BosssoftCookie } from '@/typings/login'
import config  from '@/config'
import { apiRefreshToken, saltConvert } from '@/api/login'
import  { useMenusStore } from '@/store'
import { getCookie, appLoginHandler, appLoginOutHandler } from '@/utils/app-gateway'
import { ensureRoutesData  } from '@/router/tools'
// 路由白名单
const whiteList = config.route.whiteList
export function setupPageGuard(router: Router) {
  //前置守卫
  router.beforeEach(async (to, from, next) => {
    // 内嵌页不拦截
    if(config.route.robotList.includes(to.name)) {
      next()
      return
    }
    const menusStore = useMenusStore()
    // appid
    const urlAppId = to.query?.appId
    //
    const bosssoftCookie = getCookie()
    if (bosssoftCookie) {
      // 已登录: 用户信息
      const apiData = await apiRefreshToken()
      if (apiData)  {
        let isRouteTouched = false
        if (!menusStore.dynamicRoutesState) {
          // 动态注册路由
          await ensureRoutesData(router)
          isRouteTouched = true
        }
        let _toRoute: any = undefined
        if (isRouteTouched) {
          _toRoute = { ...to, replace: true }
        }
        // 已登录： 访问登录页面，重定向到首页
        if (from.path === '/') {
          next()
          return
        }else if (to.path === '/login') {
          _toRoute = { name: 'Home', replace: true }
        }
        next(_toRoute)
      }else next({path: '/login',query: {appId: menusStore.appId}})
      //
    } else {
      // 登录url有Appid更新
      menusStore.setAppId('')
      if(to.path === '/login' && urlAppId) menusStore.setAppId(urlAppId)
       // 未登录
       if (whiteList.includes(to.path)) {
        next()
       } else {
        next({path: '/login',query: {appId: menusStore.appId}})
       }
    }
  })
}
